import sqlite3
import datetime
from typing import List, Optional, Tuple, Dict, Any
from passlib.hash import bcrypt
import logging

class DatabaseManager:
    def __init__(self, db_path: str = "policytime.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize the database with required tables"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Employees table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS employees (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    pin_hash TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Time logs table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS time_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    action TEXT NOT NULL CHECK (action IN ('SIGN_IN', 'SIGN_OUT')),
                    timestamp TIMESTAMP NOT NULL,
                    status TEXT NOT NULL CHECK (status IN ('VERIFIED', 'UNVERIFIED_OFFLINE', 'FLAGGED_SUSPICIOUS', 'AUTO_RESOLVED', 'MANUAL_CORRECTION')),
                    ntp_server TEXT,
                    local_time TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (employee_id) REFERENCES employees (id)
                )
            ''')
            
            # System configuration table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_config (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Admin password table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS admin_auth (
                    id INTEGER PRIMARY KEY,
                    password_hash TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    def setup_initial_admin(self, password: str) -> bool:
        """Set up the initial administrator password"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Check if admin already exists
                cursor.execute("SELECT COUNT(*) FROM admin_auth")
                if cursor.fetchone()[0] > 0:
                    return False
                
                # Hash password and store
                password_hash = bcrypt.hash(password)
                cursor.execute(
                    "INSERT INTO admin_auth (password_hash) VALUES (?)",
                    (password_hash,)
                )
                
                # Set default configuration
                default_config = {
                    'pin_required': 'false',
                    'hard_stop_time': '20:00',
                    'ntp_server': 'pool.ntp.org',
                    'admin_password_required': 'true'
                }
                
                for key, value in default_config.items():
                    cursor.execute(
                        "INSERT OR REPLACE INTO system_config (key, value) VALUES (?, ?)",
                        (key, value)
                    )
                
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Error setting up initial admin: {e}")
            return False
    
    def verify_admin_password(self, password: str) -> bool:
        """Verify administrator password"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT password_hash FROM admin_auth LIMIT 1")
                result = cursor.fetchone()
                
                if result:
                    return bcrypt.verify(password, result[0])
                return False
        except Exception as e:
            logging.error(f"Error verifying admin password: {e}")
            return False
    
    def change_admin_password(self, new_password: str) -> bool:
        """Change administrator password"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                password_hash = bcrypt.hash(new_password)
                cursor.execute(
                    "UPDATE admin_auth SET password_hash = ?",
                    (password_hash,)
                )
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Error changing admin password: {e}")
            return False
    
    def get_active_employees(self) -> List[Dict[str, Any]]:
        """Get all active employees"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, name, pin_hash FROM employees 
                    WHERE is_active = 1 
                    ORDER BY name
                ''')
                
                employees = []
                for row in cursor.fetchall():
                    employees.append({
                        'id': row[0],
                        'name': row[1],
                        'has_pin': bool(row[2])
                    })
                return employees
        except Exception as e:
            logging.error(f"Error getting active employees: {e}")
            return []
    
    def add_employee(self, name: str, pin: Optional[str] = None) -> bool:
        """Add a new employee"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                pin_hash = bcrypt.hash(pin) if pin else None
                cursor.execute(
                    "INSERT INTO employees (name, pin_hash) VALUES (?, ?)",
                    (name, pin_hash)
                )
                conn.commit()
                return True
        except sqlite3.IntegrityError:
            return False  # Name already exists
        except Exception as e:
            logging.error(f"Error adding employee: {e}")
            return False
    
    def update_employee(self, employee_id: int, name: str = None, pin: str = None) -> bool:
        """Update employee information"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if name:
                    cursor.execute(
                        "UPDATE employees SET name = ? WHERE id = ?",
                        (name, employee_id)
                    )
                
                if pin is not None:
                    pin_hash = bcrypt.hash(pin) if pin else None
                    cursor.execute(
                        "UPDATE employees SET pin_hash = ? WHERE id = ?",
                        (pin_hash, employee_id)
                    )
                
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Error updating employee: {e}")
            return False
    
    def deactivate_employee(self, employee_id: int) -> bool:
        """Deactivate an employee (soft delete)"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "UPDATE employees SET is_active = 0 WHERE id = ?",
                    (employee_id,)
                )
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Error deactivating employee: {e}")
            return False
    
    def verify_employee_pin(self, employee_id: int, pin: str) -> bool:
        """Verify employee PIN"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT pin_hash FROM employees WHERE id = ? AND is_active = 1",
                    (employee_id,)
                )
                result = cursor.fetchone()
                
                if result and result[0]:
                    return bcrypt.verify(pin, result[0])
                return False
        except Exception as e:
            logging.error(f"Error verifying employee PIN: {e}")
            return False
    
    def get_employee_last_action(self, employee_id: int) -> Optional[Dict[str, Any]]:
        """Get the last action for an employee"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT action, timestamp, status 
                    FROM time_logs 
                    WHERE employee_id = ? 
                    ORDER BY timestamp DESC 
                    LIMIT 1
                ''', (employee_id,))
                
                result = cursor.fetchone()
                if result:
                    return {
                        'action': result[0],
                        'timestamp': result[1],
                        'status': result[2]
                    }
                return None
        except Exception as e:
            logging.error(f"Error getting employee last action: {e}")
            return None
    
    def add_time_log(self, employee_id: int, action: str, timestamp: datetime.datetime, 
                    status: str, ntp_server: str = None, local_time: datetime.datetime = None) -> bool:
        """Add a time log entry"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO time_logs 
                    (employee_id, action, timestamp, status, ntp_server, local_time)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (employee_id, action, timestamp, status, ntp_server, local_time))
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Error adding time log: {e}")
            return False
    
    def get_time_logs(self, employee_id: int = None, start_date: str = None, 
                     end_date: str = None, status: str = None) -> List[Dict[str, Any]]:
        """Get time logs with optional filtering"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                query = '''
                    SELECT tl.id, e.name, tl.action, tl.timestamp, tl.status, 
                           tl.ntp_server, tl.local_time
                    FROM time_logs tl
                    JOIN employees e ON tl.employee_id = e.id
                    WHERE 1=1
                '''
                params = []
                
                if employee_id:
                    query += " AND tl.employee_id = ?"
                    params.append(employee_id)
                
                if start_date:
                    query += " AND DATE(tl.timestamp) >= ?"
                    params.append(start_date)
                
                if end_date:
                    query += " AND DATE(tl.timestamp) <= ?"
                    params.append(end_date)
                
                if status:
                    query += " AND tl.status = ?"
                    params.append(status)
                
                query += " ORDER BY tl.timestamp DESC"
                
                cursor.execute(query, params)
                
                logs = []
                for row in cursor.fetchall():
                    logs.append({
                        'id': row[0],
                        'employee_name': row[1],
                        'action': row[2],
                        'timestamp': row[3],
                        'status': row[4],
                        'ntp_server': row[5],
                        'local_time': row[6]
                    })
                return logs
        except Exception as e:
            logging.error(f"Error getting time logs: {e}")
            return []
    
    def update_log_status(self, log_id: int, new_status: str) -> bool:
        """Update the status of a time log entry"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "UPDATE time_logs SET status = ? WHERE id = ?",
                    (new_status, log_id)
                )
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Error updating log status: {e}")
            return False
    
    def update_time_log(self, log_id: int, new_timestamp: datetime.datetime, reason: str = None) -> bool:
        """Update a time log entry with a new timestamp (manual correction)"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Update the timestamp and set status to MANUAL_CORRECTION
                cursor.execute(
                    "UPDATE time_logs SET timestamp = ?, status = 'MANUAL_CORRECTION' WHERE id = ?",
                    (new_timestamp.isoformat(), log_id)
                )
                
                # Log the manual correction reason if provided
                if reason:
                    # You could add a separate table for correction logs if needed
                    # For now, we'll just update the status to indicate it was manually corrected
                    pass
                
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Error updating time log: {e}")
            return False
    
    def get_config(self, key: str) -> Optional[str]:
        """Get system configuration value"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT value FROM system_config WHERE key = ?",
                    (key,)
                )
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            logging.error(f"Error getting config: {e}")
            return None
    
    def set_config(self, key: str, value: str) -> bool:
        """Set system configuration value"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "INSERT OR REPLACE INTO system_config (key, value) VALUES (?, ?)",
                    (key, value)
                )
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Error setting config: {e}")
            return False
    
    def get_unverified_offline_logs(self) -> List[Dict[str, Any]]:
        """Get all unverified offline logs for audit"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT tl.id, tl.employee_id, tl.action, tl.timestamp, tl.status
                    FROM time_logs tl
                    WHERE tl.status = 'UNVERIFIED_OFFLINE'
                    ORDER BY tl.timestamp
                ''')
                
                logs = []
                for row in cursor.fetchall():
                    logs.append({
                        'id': row[0],
                        'employee_id': row[1],
                        'action': row[2],
                        'timestamp': row[3],
                        'status': row[4]
                    })
                return logs
        except Exception as e:
            logging.error(f"Error getting unverified offline logs: {e}")
            return []
    
    def admin_exists(self) -> bool:
        """Check if admin password is set up"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM admin_auth")
                return cursor.fetchone()[0] > 0
        except Exception as e:
            logging.error(f"Error checking admin existence: {e}")
            return False
    
    def delete_time_log(self, log_id: int) -> bool:
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM time_logs WHERE id = ?", (log_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            logging.error(f"Error deleting time log: {e}")
            return False 