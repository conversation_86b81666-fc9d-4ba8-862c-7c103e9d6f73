import ntplib
import datetime
import socket
import logging
from typing import Tuple, Optional, Dict, Any
from database import DatabaseManager

class TimeService:
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.ntp_client = ntplib.NTPClient()
        self.ntp_timeout = 3  # 3 seconds timeout for NTP requests
    
    def get_current_time(self) -> Tuple[datetime.datetime, str, Optional[str], Optional[datetime.datetime]]:
        """
        Get current time with NTP verification
        Returns: (timestamp, status, ntp_server, local_time)
        """
        local_time = datetime.datetime.utcnow()
        ntp_server = self.db_manager.get_config('ntp_server') or 'pool.ntp.org'
        
        try:
            # Attempt to get NTP time
            response = self.ntp_client.request(ntp_server, timeout=self.ntp_timeout)
            ntp_time = datetime.datetime.fromtimestamp(response.tx_time, tz=datetime.timezone.utc)
            
            # Convert to UTC datetime without timezone info for consistency
            ntp_time_utc = ntp_time.replace(tzinfo=None)
            
            return ntp_time_utc, 'VERIFIED', ntp_server, local_time
            
        except (ntplib.NTPException, socket.timeout, socket.gaierror) as e:
            logging.warning(f"NTP request failed: {e}")
            # Fall back to local time
            return local_time, 'UNVERIFIED_OFFLINE', None, local_time
    
    def audit_offline_entries(self) -> Dict[str, int]:
        """
        Audit unverified offline entries for consistency
        Returns: Dictionary with audit results
        """
        unverified_logs = self.db_manager.get_unverified_offline_logs()
        audit_results = {
            'verified': 0,
            'flagged_suspicious': 0,
            'total_checked': len(unverified_logs)
        }
        
        # Group logs by employee for chronological checking
        employee_logs = {}
        for log in unverified_logs:
            emp_id = log['employee_id']
            if emp_id not in employee_logs:
                employee_logs[emp_id] = []
            employee_logs[emp_id].append(log)
        
        for emp_id, logs in employee_logs.items():
            # Sort logs by timestamp
            logs.sort(key=lambda x: x['timestamp'])
            
            # Check for chronological consistency
            for i in range(len(logs) - 1):
                current_log = logs[i]
                next_log = logs[i + 1]
                
                # Check if actions alternate properly (SIGN_IN -> SIGN_OUT -> SIGN_IN, etc.)
                if current_log['action'] == next_log['action']:
                    # Same action twice in a row - suspicious
                    self.db_manager.update_log_status(current_log['id'], 'FLAGGED_SUSPICIOUS')
                    audit_results['flagged_suspicious'] += 1
                else:
                    # Actions alternate properly - verify this entry
                    self.db_manager.update_log_status(current_log['id'], 'VERIFIED')
                    audit_results['verified'] += 1
            
            # Handle the last log entry
            if logs:
                last_log = logs[-1]
                # If the last action was SIGN_IN, it might be legitimate (employee still signed in)
                # If it was SIGN_OUT, verify it
                if last_log['action'] == 'SIGN_OUT':
                    self.db_manager.update_log_status(last_log['id'], 'VERIFIED')
                    audit_results['verified'] += 1
                else:
                    # Last action was SIGN_IN - this could be legitimate if employee is still signed in
                    # We'll leave it as UNVERIFIED_OFFLINE for now
                    pass
        
        return audit_results
    
    def create_hard_stop_entry(self, employee_id: int, previous_date: datetime.date) -> bool:
        """
        Create an automatic sign-out entry for the hard stop time
        """
        try:
            hard_stop_time_str = self.db_manager.get_config('hard_stop_time') or '20:00'
            hour, minute = map(int, hard_stop_time_str.split(':'))
            
            # Create datetime for the hard stop time on the previous date
            hard_stop_datetime = datetime.datetime.combine(
                previous_date,
                datetime.time(hour, minute)
            )
            
            # Add the automatic sign-out entry
            success = self.db_manager.add_time_log(
                employee_id=employee_id,
                action='SIGN_OUT',
                timestamp=hard_stop_datetime,
                status='AUTO_RESOLVED',
                ntp_server=None,
                local_time=hard_stop_datetime
            )
            
            return success
            
        except Exception as e:
            logging.error(f"Error creating hard stop entry: {e}")
            return False
    
    def check_and_resolve_forgotten_signout(self, employee_id: int) -> bool:
        """
        Check if employee has a forgotten sign-out and resolve it
        Returns True if resolution was needed and successful
        """
        try:
            last_action = self.db_manager.get_employee_last_action(employee_id)
            
            if not last_action:
                return False  # No previous actions
            
            # Check if last action was a SIGN_IN from a previous day
            last_timestamp = datetime.datetime.fromisoformat(last_action['timestamp'].replace('Z', '+00:00'))
            current_date = datetime.datetime.utcnow().date()
            last_date = last_timestamp.date()
            
            if (last_action['action'] == 'SIGN_IN' and 
                last_date < current_date):
                
                # Employee has a forgotten sign-out from a previous day
                logging.info(f"Resolving forgotten sign-out for employee {employee_id} from {last_date}")
                return self.create_hard_stop_entry(employee_id, last_date)
            
            return False
            
        except Exception as e:
            logging.error(f"Error checking for forgotten sign-out: {e}")
            return False
    
    def validate_pin(self, pin: str) -> bool:
        """
        Validate PIN strength (reject common/weak PINs)
        """
        if not pin or len(pin) != 4:
            return False
        
        # Reject common/weak PINs
        weak_pins = {
            '0000', '1111', '2222', '3333', '4444', '5555', '6666', '7777', '8888', '9999',
            '1234', '2345', '3456', '4567', '5678', '6789', '7890',
            '0123', '1230', '2301', '3012',
            '1111', '2222', '3333', '4444', '5555', '6666', '7777', '8888', '9999'
        }
        
        return pin not in weak_pins
    
    def format_timestamp(self, timestamp: datetime.datetime) -> str:
        """
        Format timestamp for display in 12-hour format
        """
        return timestamp.strftime("%Y-%m-%d %I:%M:%S %p")
    
    def format_time_12hr(self, timestamp: datetime.datetime) -> str:
        """
        Format time only in 12-hour format (HH:MM:SS AM/PM)
        """
        return timestamp.strftime("%I:%M:%S %p")
    
    def format_time_12hr_short(self, timestamp: datetime.datetime) -> str:
        """
        Format time in 12-hour format without seconds (HH:MM AM/PM)
        """
        return timestamp.strftime("%I:%M %p")
    
    def get_time_difference(self, time1: datetime.datetime, time2: datetime.datetime) -> str:
        """
        Get human-readable time difference between two timestamps
        """
        diff = abs(time2 - time1)
        hours = diff.total_seconds() / 3600
        
        if hours < 1:
            minutes = diff.total_seconds() / 60
            return f"{minutes:.0f} minutes"
        elif hours < 24:
            return f"{hours:.1f} hours"
        else:
            days = hours / 24
            return f"{days:.1f} days" 