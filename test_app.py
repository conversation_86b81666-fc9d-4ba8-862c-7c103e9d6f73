#!/usr/bin/env python3
"""
Test script for PolicyTime application
This script tests the core functionality without running the full GUI
"""

import os
import sys
import datetime
from database import DatabaseManager
from time_service import TimeService

def test_database():
    """Test database functionality"""
    print("Testing database functionality...")
    
    # Create a test database
    test_db = "test_policytime.db"
    db_manager = DatabaseManager(test_db)
    
    # Test admin setup
    assert db_manager.setup_initial_admin("testpassword123"), "Admin setup failed"
    assert db_manager.verify_admin_password("testpassword123"), "Admin password verification failed"
    assert not db_manager.verify_admin_password("wrongpassword"), "Wrong password should fail"
    
    # Test employee management
    assert db_manager.add_employee("<PERSON>", "1234"), "Add employee failed"
    assert db_manager.add_employee("<PERSON>", "5678"), "Add second employee failed"
    assert not db_manager.add_employee("<PERSON>", "9999"), "Duplicate employee should fail"
    
    employees = db_manager.get_active_employees()
    assert len(employees) == 2, f"Expected 2 employees, got {len(employees)}"
    
    # Test PIN verification
    assert db_manager.verify_employee_pin(employees[0]['id'], "1234"), "PIN verification failed"
    assert not db_manager.verify_employee_pin(employees[0]['id'], "9999"), "Wrong PIN should fail"
    
    # Test time logging
    current_time = datetime.datetime.utcnow()
    assert db_manager.add_time_log(
        employees[0]['id'], 
        "SIGN_IN", 
        current_time, 
        "VERIFIED"
    ), "Time log addition failed"
    
    # Test configuration
    assert db_manager.set_config("test_key", "test_value"), "Config set failed"
    assert db_manager.get_config("test_key") == "test_value", "Config get failed"
    
    # Clean up
    if os.path.exists(test_db):
        os.remove(test_db)
    
    print("Database tests passed!")

def test_time_service():
    """Test time service functionality"""
    print("Testing time service functionality...")
    
    # Create test database and time service
    test_db = "test_policytime.db"
    db_manager = DatabaseManager(test_db)
    db_manager.setup_initial_admin("testpassword123")
    db_manager.add_employee("Test Employee", "1234")
    
    time_service = TimeService(db_manager)
    
    # Test time retrieval
    current_time, status, ntp_server, local_time = time_service.get_current_time()
    assert isinstance(current_time, datetime.datetime), "Time should be datetime object"
    assert status in ["VERIFIED", "UNVERIFIED_OFFLINE"], f"Invalid status: {status}"
    
    # Test PIN validation
    assert time_service.validate_pin("1234"), "Valid PIN should pass"
    assert not time_service.validate_pin("123"), "Short PIN should fail"
    assert not time_service.validate_pin("12345"), "Long PIN should fail"
    assert not time_service.validate_pin("0000"), "Weak PIN should fail"
    assert not time_service.validate_pin("1234"), "Common PIN should fail"
    
    # Test hard stop entry creation
    employee_id = db_manager.get_active_employees()[0]['id']
    yesterday = datetime.datetime.utcnow().date() - datetime.timedelta(days=1)
    assert time_service.create_hard_stop_entry(employee_id, yesterday), "Hard stop entry creation failed"
    
    # Clean up
    if os.path.exists(test_db):
        os.remove(test_db)
    
    print("Time service tests passed!")

def test_integration():
    """Test integration between components"""
    print("Testing integration...")
    
    # Create test database and services
    test_db = "test_policytime.db"
    db_manager = DatabaseManager(test_db)
    db_manager.setup_initial_admin("testpassword123")
    db_manager.add_employee("Integration Test Employee", "1234")
    
    time_service = TimeService(db_manager)
    employee = db_manager.get_active_employees()[0]
    
    # Test complete sign-in/out flow
    current_time, status, ntp_server, local_time = time_service.get_current_time()
    
    # Sign in
    assert db_manager.add_time_log(
        employee['id'], 
        "SIGN_IN", 
        current_time, 
        status,
        ntp_server,
        local_time
    ), "Sign in failed"
    
    # Check last action
    last_action = db_manager.get_employee_last_action(employee['id'])
    assert last_action['action'] == "SIGN_IN", "Last action should be SIGN_IN"
    
    # Sign out
    current_time, status, ntp_server, local_time = time_service.get_current_time()
    assert db_manager.add_time_log(
        employee['id'], 
        "SIGN_OUT", 
        current_time, 
        status,
        ntp_server,
        local_time
    ), "Sign out failed"
    
    # Check logs
    logs = db_manager.get_time_logs(employee_id=employee['id'])
    assert len(logs) == 2, f"Expected 2 logs, got {len(logs)}"
    assert logs[0]['action'] == "SIGN_OUT", "Latest log should be SIGN_OUT"
    assert logs[1]['action'] == "SIGN_IN", "Previous log should be SIGN_IN"
    
    # Clean up
    if os.path.exists(test_db):
        os.remove(test_db)
    
    print("Integration tests passed!")

def main():
    """Run all tests"""
    print("Starting PolicyTime application tests...")
    print("=" * 50)
    
    try:
        test_database()
        test_time_service()
        test_integration()
        
        print("=" * 50)
        print("All tests passed! The application components are working correctly.")
        
    except Exception as e:
        print(f"Test failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 